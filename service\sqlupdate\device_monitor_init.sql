-- 设备监控系统初始化SQL脚本（包含S7 PLC支持）

-- 创建PLC配置表
DROP TABLE IF EXISTS `plc_config`;
CREATE TABLE `plc_config` (
  `id` varchar(36) NOT NULL COMMENT '主键ID',
  `plc_name` varchar(100) NOT NULL COMMENT 'PLC名称',
  `ip_address` varchar(50) NOT NULL COMMENT 'IP地址',
  `port` int(5) DEFAULT '102' COMMENT '端口号',
  `rack` int(2) DEFAULT '0' COMMENT '机架号',
  `slot` int(2) DEFAULT '1' COMMENT '插槽号',
  `connection_timeout` int(10) DEFAULT '5000' COMMENT '连接超时时间(毫秒)',
  `read_timeout` int(10) DEFAULT '3000' COMMENT '读取超时时间(毫秒)',
  `enabled` int(1) DEFAULT '1' COMMENT '是否启用：0-禁用，1-启用',
  `connection_status` int(1) DEFAULT '0' COMMENT '连接状态：0-断开，1-连接',
  `last_connection_time` datetime DEFAULT NULL COMMENT '最后连接时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_plc_name` (`plc_name`),
  KEY `idx_ip_address` (`ip_address`),
  KEY `idx_enabled` (`enabled`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='PLC配置表';

-- 创建PLC数据点位配置表
DROP TABLE IF EXISTS `plc_data_point`;
CREATE TABLE `plc_data_point` (
  `id` varchar(36) NOT NULL COMMENT '主键ID',
  `plc_id` varchar(36) NOT NULL COMMENT 'PLC配置ID',
  `device_id` varchar(36) NOT NULL COMMENT '设备ID',
  `point_name` varchar(100) NOT NULL COMMENT '点位名称',
  `point_desc` varchar(200) DEFAULT NULL COMMENT '点位描述',
  `data_area` varchar(10) NOT NULL COMMENT '数据区域：DB-数据块，I-输入，Q-输出，M-内存，T-定时器，C-计数器',
  `db_number` int(5) DEFAULT NULL COMMENT '数据块号（DB区域时使用）',
  `start_address` int(10) NOT NULL COMMENT '起始地址',
  `data_type` varchar(20) NOT NULL COMMENT '数据类型：BOOL, BYTE, WORD, DWORD, INT, DINT, REAL, STRING',
  `string_length` int(5) DEFAULT NULL COMMENT '字符串长度（STRING类型时使用）',
  `bit_offset` int(1) DEFAULT NULL COMMENT '位偏移（BOOL类型时使用）',
  `unit` varchar(20) DEFAULT NULL COMMENT '数据单位',
  `scale_factor` double DEFAULT '1' COMMENT '缩放因子',
  `offset` double DEFAULT '0' COMMENT '偏移量',
  `monitor_item` varchar(100) DEFAULT NULL COMMENT '监控项目（对应设备监控数据项）',
  `enabled` int(1) DEFAULT '1' COMMENT '是否启用：0-禁用，1-启用',
  `collect_interval` int(10) DEFAULT '30' COMMENT '采集周期(秒)',
  `min_value` double DEFAULT NULL COMMENT '最小值',
  `max_value` double DEFAULT NULL COMMENT '最大值',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_plc_id` (`plc_id`),
  KEY `idx_device_id` (`device_id`),
  KEY `idx_point_name` (`point_name`),
  KEY `idx_enabled` (`enabled`),
  CONSTRAINT `fk_plc_data_point_plc` FOREIGN KEY (`plc_id`) REFERENCES `plc_config` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='PLC数据点位配置表';

-- 创建设备监控表
DROP TABLE IF EXISTS `device_monitor`;
CREATE TABLE `device_monitor` (
  `id` varchar(36) NOT NULL COMMENT '主键ID',
  `device_code` varchar(50) NOT NULL COMMENT '设备编号',
  `device_name` varchar(100) NOT NULL COMMENT '设备名称',
  `device_type` varchar(50) DEFAULT NULL COMMENT '设备类型',
  `status` int(1) DEFAULT '0' COMMENT '设备状态：0-离线，1-在线，2-故障，3-维护',
  `run_status` int(1) DEFAULT '0' COMMENT '运行状态：0-停止，1-运行，2-异常',
  `location` varchar(200) DEFAULT NULL COMMENT '设备位置',
  `load_capacity` varchar(100) DEFAULT NULL COMMENT '负载能力',
  `oil_analysis` varchar(100) DEFAULT NULL COMMENT '油位清洁分析',
  `cooling_life` varchar(100) DEFAULT NULL COMMENT '制冷寿命',
  `intelligent_evaluation` varchar(100) DEFAULT NULL COMMENT '智能评价',
  `voltage` decimal(10,2) DEFAULT NULL COMMENT '电压(V)',
  `current` decimal(10,2) DEFAULT NULL COMMENT '电流(A)',
  `temperature` decimal(5,1) DEFAULT NULL COMMENT '温度(℃)',
  `last_update_time` datetime DEFAULT NULL COMMENT '最后更新时间',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_device_code` (`device_code`),
  KEY `idx_device_type` (`device_type`),
  KEY `idx_status` (`status`),
  KEY `idx_run_status` (`run_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备监控表';

-- 创建设备监控数据表
DROP TABLE IF EXISTS `device_monitor_data`;
CREATE TABLE `device_monitor_data` (
  `id` varchar(36) NOT NULL COMMENT '主键ID',
  `device_id` varchar(36) NOT NULL COMMENT '设备ID',
  `device_code` varchar(50) NOT NULL COMMENT '设备编号',
  `monitor_item` varchar(100) NOT NULL COMMENT '监测项目名称',
  `monitor_value` decimal(15,4) DEFAULT NULL COMMENT '监测值',
  `unit` varchar(20) DEFAULT NULL COMMENT '单位',
  `min_value` decimal(15,4) DEFAULT NULL COMMENT '正常范围最小值',
  `max_value` decimal(15,4) DEFAULT NULL COMMENT '正常范围最大值',
  `status` int(1) DEFAULT '0' COMMENT '状态：0-正常，1-预警，2-异常',
  `monitor_time` datetime NOT NULL COMMENT '监测时间',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_device_id` (`device_id`),
  KEY `idx_device_code` (`device_code`),
  KEY `idx_monitor_item` (`monitor_item`),
  KEY `idx_monitor_time` (`monitor_time`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备监控数据表';

-- 插入字典数据
-- 设备类型字典
INSERT INTO `sys_dict` (`id`, `dict_name`, `dict_code`, `description`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `type`) VALUES 
('device_type_dict', '设备类型', 'device_type', '设备类型字典', 0, 'admin', NOW(), 'admin', NOW(), 0);

INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES 
('device_type_1', 'device_type_dict', '变压器', 'transformer', '变压器设备', 1, 1, 'admin', NOW(), 'admin', NOW()),
('device_type_2', 'device_type_dict', '开关柜', 'switchgear', '开关柜设备', 2, 1, 'admin', NOW(), 'admin', NOW()),
('device_type_3', 'device_type_dict', '电容器', 'capacitor', '电容器设备', 3, 1, 'admin', NOW(), 'admin', NOW()),
('device_type_4', 'device_type_dict', '电抗器', 'reactor', '电抗器设备', 4, 1, 'admin', NOW(), 'admin', NOW());

-- 设备状态字典
INSERT INTO `sys_dict` (`id`, `dict_name`, `dict_code`, `description`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `type`) VALUES 
('device_status_dict', '设备状态', 'device_status', '设备状态字典', 0, 'admin', NOW(), 'admin', NOW(), 0);

INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES 
('device_status_0', 'device_status_dict', '离线', '0', '设备离线', 1, 1, 'admin', NOW(), 'admin', NOW()),
('device_status_1', 'device_status_dict', '在线', '1', '设备在线', 2, 1, 'admin', NOW(), 'admin', NOW()),
('device_status_2', 'device_status_dict', '故障', '2', '设备故障', 3, 1, 'admin', NOW(), 'admin', NOW()),
('device_status_3', 'device_status_dict', '维护', '3', '设备维护', 4, 1, 'admin', NOW(), 'admin', NOW());

-- 设备运行状态字典
INSERT INTO `sys_dict` (`id`, `dict_name`, `dict_code`, `description`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `type`) VALUES
('device_run_status_dict', '设备运行状态', 'device_run_status', '设备运行状态字典', 0, 'admin', NOW(), 'admin', NOW(), 0);

INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES
('device_run_status_0', 'device_run_status_dict', '停止', '0', '设备停止运行', 1, 1, 'admin', NOW(), 'admin', NOW()),
('device_run_status_1', 'device_run_status_dict', '运行', '1', '设备正常运行', 2, 1, 'admin', NOW(), 'admin', NOW()),
('device_run_status_2', 'device_run_status_dict', '异常', '2', '设备运行异常', 3, 1, 'admin', NOW(), 'admin', NOW());

-- PLC数据区域字典
INSERT INTO `sys_dict` (`id`, `dict_name`, `dict_code`, `description`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `type`) VALUES
('plc_data_area_dict', 'PLC数据区域', 'plc_data_area', 'PLC数据区域字典', 0, 'admin', NOW(), 'admin', NOW(), 0);

INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES
('plc_data_area_db', 'plc_data_area_dict', '数据块', 'DB', 'DB数据块区域', 1, 1, 'admin', NOW(), 'admin', NOW()),
('plc_data_area_i', 'plc_data_area_dict', '输入', 'I', 'I输入区域', 2, 1, 'admin', NOW(), 'admin', NOW()),
('plc_data_area_q', 'plc_data_area_dict', '输出', 'Q', 'Q输出区域', 3, 1, 'admin', NOW(), 'admin', NOW()),
('plc_data_area_m', 'plc_data_area_dict', '内存', 'M', 'M内存区域', 4, 1, 'admin', NOW(), 'admin', NOW()),
('plc_data_area_t', 'plc_data_area_dict', '定时器', 'T', 'T定时器区域', 5, 1, 'admin', NOW(), 'admin', NOW()),
('plc_data_area_c', 'plc_data_area_dict', '计数器', 'C', 'C计数器区域', 6, 1, 'admin', NOW(), 'admin', NOW());

-- PLC数据类型字典
INSERT INTO `sys_dict` (`id`, `dict_name`, `dict_code`, `description`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `type`) VALUES
('plc_data_type_dict', 'PLC数据类型', 'plc_data_type', 'PLC数据类型字典', 0, 'admin', NOW(), 'admin', NOW(), 0);

INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES
('plc_data_type_bool', 'plc_data_type_dict', '布尔型', 'BOOL', '布尔型数据', 1, 1, 'admin', NOW(), 'admin', NOW()),
('plc_data_type_byte', 'plc_data_type_dict', '字节型', 'BYTE', '字节型数据', 2, 1, 'admin', NOW(), 'admin', NOW()),
('plc_data_type_word', 'plc_data_type_dict', '字型', 'WORD', '字型数据', 3, 1, 'admin', NOW(), 'admin', NOW()),
('plc_data_type_dword', 'plc_data_type_dict', '双字型', 'DWORD', '双字型数据', 4, 1, 'admin', NOW(), 'admin', NOW()),
('plc_data_type_int', 'plc_data_type_dict', '整型', 'INT', '整型数据', 5, 1, 'admin', NOW(), 'admin', NOW()),
('plc_data_type_dint', 'plc_data_type_dict', '双整型', 'DINT', '双整型数据', 6, 1, 'admin', NOW(), 'admin', NOW()),
('plc_data_type_real', 'plc_data_type_dict', '实数型', 'REAL', '实数型数据', 7, 1, 'admin', NOW(), 'admin', NOW()),
('plc_data_type_string', 'plc_data_type_dict', '字符串', 'STRING', '字符串数据', 8, 1, 'admin', NOW(), 'admin', NOW());

-- 插入示例设备数据
INSERT INTO `device_monitor` (`id`, `device_code`, `device_name`, `device_type`, `status`, `run_status`, `location`, `load_capacity`, `oil_analysis`, `cooling_life`, `intelligent_evaluation`, `voltage`, `current`, `temperature`, `last_update_time`, `create_by`, `create_time`) VALUES 
('device_001', 'DEV001', '主变压器A', 'transformer', 1, 1, '变电站A区', '正常', '正常', '5年', '正常', 500.00, 120.50, 35.2, NOW(), 'admin', NOW()),
('device_002', 'DEV002', '主变压器B', 'transformer', 1, 1, '变电站B区', '正常', '正常', '3年', '正常', 500.00, 115.30, 36.1, NOW(), 'admin', NOW()),
('device_003', 'DEV003', '开关柜1号', 'switchgear', 1, 1, '变电站C区', '正常', '正常', '8年', '正常', 500.00, 0.00, 32.5, NOW(), 'admin', NOW()),
('device_004', 'DEV004', '开关柜2号', 'switchgear', 2, 2, '变电站D区', '异常', '正常', '2年', '异常', 480.00, 0.00, 45.8, NOW(), 'admin', NOW()),
('device_005', 'DEV005', '电容器组A', 'capacitor', 1, 1, '变电站E区', '正常', '正常', '6年', '正常', 500.00, 85.20, 28.9, NOW(), 'admin', NOW());

-- 插入示例监控数据
INSERT INTO `device_monitor_data` (`id`, `device_id`, `device_code`, `monitor_item`, `monitor_value`, `unit`, `min_value`, `max_value`, `status`, `monitor_time`, `create_time`) VALUES 
('data_001', 'device_001', 'DEV001', '负载能力', 85.5, '%', 0, 100, 0, NOW(), NOW()),
('data_002', 'device_001', 'DEV001', '油位清洁', 95.2, '%', 80, 100, 0, NOW(), NOW()),
('data_003', 'device_001', 'DEV001', '制冷效率', 92.8, '%', 85, 100, 0, NOW(), NOW()),
('data_004', 'device_002', 'DEV002', '负载能力', 78.3, '%', 0, 100, 0, NOW(), NOW()),
('data_005', 'device_002', 'DEV002', '油位清洁', 88.7, '%', 80, 100, 0, NOW(), NOW()),
('data_006', 'device_003', 'DEV003', '负载能力', 65.4, '%', 0, 100, 0, NOW(), NOW()),
('data_007', 'device_004', 'DEV004', '负载能力', 45.2, '%', 0, 100, 1, NOW(), NOW()),
('data_008', 'device_005', 'DEV005', '负载能力', 92.1, '%', 0, 100, 0, NOW(), NOW());

-- 添加菜单权限
-- 主菜单
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_route`, `is_leaf`, `keep_alive`, `hidden`, `hide_tab`, `description`, `status`, `del_flag`, `rule_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `internal_or_external`) VALUES 
('shebei_monitor_menu', NULL, '设备监控', '/shebei', 'layouts/default/index', NULL, '/shebei/monitor/list', 0, NULL, '1', 1.00, 0, 'ant-design:monitor-outlined', 1, 0, 0, 0, 0, NULL, '1', 0, 0, 'admin', NOW(), 'admin', NOW(), 0);

-- 设备监控管理菜单
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_route`, `is_leaf`, `keep_alive`, `hidden`, `hide_tab`, `description`, `status`, `del_flag`, `rule_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `internal_or_external`) VALUES 
('shebei_monitor_list', 'shebei_monitor_menu', '设备监控管理', '/shebei/monitor/list', 'shebei/monitor/DeviceMonitorList', NULL, NULL, 1, NULL, '1', 1.00, 0, 'ant-design:unordered-list-outlined', 1, 1, 0, 0, 0, NULL, '1', 0, 0, 'admin', NOW(), 'admin', NOW(), 0);

-- 监控大屏菜单
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_route`, `is_leaf`, `keep_alive`, `hidden`, `hide_tab`, `description`, `status`, `del_flag`, `rule_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `internal_or_external`) VALUES
('shebei_monitor_dashboard', 'shebei_monitor_menu', '监控大屏', '/shebei/monitor/dashboard', 'shebei/monitor/DeviceMonitorDashboard', NULL, NULL, 1, NULL, '1', 2.00, 0, 'ant-design:dashboard-outlined', 1, 1, 0, 0, 0, NULL, '1', 0, 0, 'admin', NOW(), 'admin', NOW(), 0);

-- PLC配置管理菜单
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_route`, `is_leaf`, `keep_alive`, `hidden`, `hide_tab`, `description`, `status`, `del_flag`, `rule_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `internal_or_external`) VALUES
('shebei_plc_config', 'shebei_monitor_menu', 'PLC配置管理', '/shebei/plc/config', 'shebei/plc/PlcConfigList', NULL, NULL, 1, NULL, '1', 3.00, 0, 'ant-design:setting-outlined', 1, 1, 0, 0, 0, NULL, '1', 0, 0, 'admin', NOW(), 'admin', NOW(), 0);

-- 数据点位配置管理菜单
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_route`, `is_leaf`, `keep_alive`, `hidden`, `hide_tab`, `description`, `status`, `del_flag`, `rule_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `internal_or_external`) VALUES
('shebei_datapoint_config', 'shebei_monitor_menu', '数据点位配置', '/shebei/datapoint/config', 'shebei/datapoint/PlcDataPointList', NULL, NULL, 1, NULL, '1', 4.00, 0, 'ant-design:node-index-outlined', 1, 1, 0, 0, 0, NULL, '1', 0, 0, 'admin', NOW(), 'admin', NOW(), 0);

-- 按钮权限
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_route`, `is_leaf`, `keep_alive`, `hidden`, `hide_tab`, `description`, `status`, `del_flag`, `rule_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `internal_or_external`) VALUES 
('shebei_monitor_add', 'shebei_monitor_list', '添加', NULL, NULL, NULL, NULL, 2, 'shebei:deviceMonitor:add', '1', 1.00, 0, NULL, 1, 1, 0, 0, 0, NULL, '1', 0, 0, 'admin', NOW(), 'admin', NOW(), 0),
('shebei_monitor_edit', 'shebei_monitor_list', '编辑', NULL, NULL, NULL, NULL, 2, 'shebei:deviceMonitor:edit', '1', 2.00, 0, NULL, 1, 1, 0, 0, 0, NULL, '1', 0, 0, 'admin', NOW(), 'admin', NOW(), 0),
('shebei_monitor_delete', 'shebei_monitor_list', '删除', NULL, NULL, NULL, NULL, 2, 'shebei:deviceMonitor:delete', '1', 3.00, 0, NULL, 1, 1, 0, 0, 0, NULL, '1', 0, 0, 'admin', NOW(), 'admin', NOW(), 0),
('shebei_monitor_query', 'shebei_monitor_list', '查询', NULL, NULL, NULL, NULL, 2, 'shebei:deviceMonitor:query', '1', 4.00, 0, NULL, 1, 1, 0, 0, 0, NULL, '1', 0, 0, 'admin', NOW(), 'admin', NOW(), 0),
('shebei_monitor_export', 'shebei_monitor_list', '导出', NULL, NULL, NULL, NULL, 2, 'shebei:deviceMonitor:export', '1', 5.00, 0, NULL, 1, 1, 0, 0, 0, NULL, '1', 0, 0, 'admin', NOW(), 'admin', NOW(), 0),
('shebei_monitor_import', 'shebei_monitor_list', '导入', NULL, NULL, NULL, NULL, 2, 'shebei:deviceMonitor:import', '1', 6.00, 0, NULL, 1, 1, 0, 0, 0, NULL, '1', 0, 0, 'admin', NOW(), 'admin', NOW(), 0);
