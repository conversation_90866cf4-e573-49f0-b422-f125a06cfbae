# PLC数据块偏移量对应表

## DB1数据块结构详细说明

### 原始数据字节对应表
```
字节位置  十六进制示例  数据含义              对应配置
---------|------------|---------------------|---------------------------
0        FF           设备1温度低字节       start_address=0, INT类型
1        00           设备1温度高字节       (255 = 25.5°C, scale=0.1)
2        78           设备1压力低字节       start_address=2, INT类型  
3        00           设备1压力高字节       (120 = 1.2MPa, scale=0.01)
4        42           设备1流量字节1        start_address=4, REAL类型
5        C8           设备1流量字节2        (100.5L/min)
6        80           设备1流量字节3        
7        00           设备1流量字节4        
8        01           设备1状态字节         start_address=8, BOOL类型
         位0=1        运行状态=true         bit_offset=0
         位1=0        故障状态=false        bit_offset=1  
         位2=0        维护状态=false        bit_offset=2
9        00           (保留字节)
10       2E           设备2温度低字节       start_address=10, INT类型
11       01           设备2温度高字节       (302 = 30.2°C, scale=0.1)
12       96           设备2压力低字节       start_address=12, INT类型
13       00           设备2压力高字节       (150 = 1.5MPa, scale=0.01)
14       42           设备2流量字节1        start_address=14, REAL类型
15       AA           设备2流量字节2        (85.3L/min)
16       66           设备2流量字节3        
17       66           设备2流量字节4        
18       00           设备2状态字节         start_address=18, BOOL类型
         位0=0        运行状态=false        bit_offset=0
         位1=0        故障状态=false        bit_offset=1
         位2=0        维护状态=false        bit_offset=2
19       00           (保留字节)
```

## 数据类型占用字节数说明

| 数据类型 | 占用字节数 | 说明 |
|---------|-----------|------|
| BOOL    | 1         | 布尔型，可用位偏移在同一字节存储多个值 |
| BYTE    | 1         | 字节型 |
| WORD    | 2         | 字型，无符号16位整数 |
| INT     | 2         | 整型，有符号16位整数 |
| DWORD   | 4         | 双字型，无符号32位整数 |
| DINT    | 4         | 双整型，有符号32位整数 |
| REAL    | 4         | 实数型，32位浮点数 |
| STRING  | 可变      | 字符串，前2字节为长度信息 |

## 实际配置示例

### 设备1配置 (偏移量0-8)
```sql
-- 温度：偏移量0，占用字节0-1
INSERT INTO plc_data_point VALUES 
('point_001', 'plc_001', 'device_001', '设备1温度', '设备1实时温度监控', 
 'DB', 1, 0, 'INT', NULL, NULL, '°C', 0.1, 0, '温度', 1, 30, -50, 150, 'admin', NOW());

-- 压力：偏移量2，占用字节2-3  
INSERT INTO plc_data_point VALUES
('point_002', 'plc_001', 'device_001', '设备1压力', '设备1实时压力监控', 
 'DB', 1, 2, 'INT', NULL, NULL, 'MPa', 0.01, 0, '压力', 1, 30, 0, 10, 'admin', NOW());

-- 流量：偏移量4，占用字节4-7
INSERT INTO plc_data_point VALUES
('point_003', 'plc_001', 'device_001', '设备1流量', '设备1实时流量监控', 
 'DB', 1, 4, 'REAL', NULL, NULL, 'L/min', 1.0, 0, '流量', 1, 30, 0, 1000, 'admin', NOW());

-- 运行状态：偏移量8，占用字节8，位0
INSERT INTO plc_data_point VALUES
('point_004', 'plc_001', 'device_001', '设备1运行状态', '设备1运行状态监控', 
 'DB', 1, 8, 'BOOL', NULL, 0, '', 1.0, 0, '运行状态', 1, 10, NULL, NULL, 'admin', NOW());

-- 故障状态：偏移量8，占用字节8，位1
INSERT INTO plc_data_point VALUES
('point_005', 'plc_001', 'device_001', '设备1故障状态', '设备1故障状态监控', 
 'DB', 1, 8, 'BOOL', NULL, 1, '', 1.0, 0, '故障状态', 1, 10, NULL, NULL, 'admin', NOW());
```

### 设备2配置 (偏移量10-18)
```sql
-- 温度：偏移量10，占用字节10-11
INSERT INTO plc_data_point VALUES
('point_007', 'plc_001', 'device_002', '设备2温度', '设备2实时温度监控', 
 'DB', 1, 10, 'INT', NULL, NULL, '°C', 0.1, 0, '温度', 1, 30, -50, 150, 'admin', NOW());

-- 压力：偏移量12，占用字节12-13
INSERT INTO plc_data_point VALUES
('point_008', 'plc_001', 'device_002', '设备2压力', '设备2实时压力监控', 
 'DB', 1, 12, 'INT', NULL, NULL, 'MPa', 0.01, 0, '压力', 1, 30, 0, 10, 'admin', NOW());

-- 流量：偏移量14，占用字节14-17
INSERT INTO plc_data_point VALUES
('point_009', 'plc_001', 'device_002', '设备2流量', '设备2实时流量监控', 
 'DB', 1, 14, 'REAL', NULL, NULL, 'L/min', 1.0, 0, '流量', 1, 30, 0, 1000, 'admin', NOW());

-- 运行状态：偏移量18，占用字节18，位0
INSERT INTO plc_data_point VALUES
('point_010', 'plc_001', 'device_002', '设备2运行状态', '设备2运行状态监控', 
 'DB', 1, 18, 'BOOL', NULL, 0, '', 1.0, 0, '运行状态', 1, 10, NULL, NULL, 'admin', NOW());
```

## 关键理解点

1. **偏移量 = 字节位置**
   - `start_address` 字段就是数据在字节数组中的起始位置
   - 例如：`start_address=0` 表示从第0个字节开始读取

2. **数据类型决定占用字节数**
   - INT类型占用2字节，所以温度(偏移量0)占用字节0-1，压力(偏移量2)占用字节2-3
   - REAL类型占用4字节，所以流量(偏移量4)占用字节4-7

3. **BOOL类型的位偏移**
   - 多个BOOL值可以共享同一个字节
   - `bit_offset` 指定在该字节中的位位置
   - 例如：偏移量8的字节，位0是运行状态，位1是故障状态，位2是维护状态

4. **避免数据重叠**
   - 设备1数据占用字节0-8，设备2数据从字节10开始，中间留出字节9作为缓冲
   - 确保不同数据点的字节范围不重叠

5. **缩放因子的作用**
   - 原始数据255，缩放因子0.1，实际值 = 255 * 0.1 = 25.5°C
   - 原始数据120，缩放因子0.01，实际值 = 120 * 0.01 = 1.2MPa

## API调用示例

```http
GET /shebei/deviceMonitor/readPlcDataByOffset?plcId=plc_001&dbNumber=1&startAddress=0&dataLength=20
```

这个API调用会：
1. 读取DB1数据块的前20个字节 (字节0-19)
2. 根据配置的偏移量解析出各个设备的数据
3. 应用缩放因子得到实际值
4. 返回解析后的结构化数据