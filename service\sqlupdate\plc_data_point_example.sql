-- PLC数据点位配置示例SQL
-- 演示如何根据偏移量配置PLC数据块中的数据点位
-- 
-- DB1数据块结构说明：
-- 偏移量0-1:   设备1温度 (INT, 2字节)     → 对应原始数据的第0-1字节
-- 偏移量2-3:   设备1压力 (INT, 2字节)     → 对应原始数据的第2-3字节  
-- 偏移量4-7:   设备1流量 (REAL, 4字节)    → 对应原始数据的第4-7字节
-- 偏移量8:     设备1状态 (BOOL, 1字节)    → 对应原始数据的第8字节
--              位0: 运行状态, 位1: 故障状态, 位2: 维护状态
-- 偏移量10-11: 设备2温度 (INT, 2字节)     → 对应原始数据的第10-11字节
-- 偏移量12-13: 设备2压力 (INT, 2字节)     → 对应原始数据的第12-13字节
-- 偏移量14-17: 设备2流量 (REAL, 4字节)    → 对应原始数据的第14-17字节
-- 偏移量18:    设备2状态 (BOOL, 1字节)    → 对应原始数据的第18字节
--              位0: 运行状态, 位1: 故障状态, 位2: 维护状态

-- 插入PLC配置示例
INSERT INTO `plc_config` (`id`, `plc_name`, `ip_address`, `port`, `rack`, `slot`, `connection_timeout`, `read_timeout`, `enabled`, `connection_status`, `last_connection_time`, `remark`, `create_by`, `create_time`) VALUES 
('plc_001', '主控PLC', '*************', 102, 0, 1, 5000, 3000, 1, 1, NOW(), '主控PLC，负责设备监控数据采集', 'admin', NOW()),
('plc_002', '备用PLC', '*************', 102, 0, 1, 5000, 3000, 1, 0, NULL, '备用PLC，故障时切换', 'admin', NOW());

-- 插入PLC数据点位配置示例
-- 示例1：设备1的监控数据点位配置（DB1数据块，偏移量0-8）
INSERT INTO `plc_data_point` (`id`, `plc_id`, `device_id`, `point_name`, `point_desc`, `data_area`, `db_number`, `start_address`, `data_type`, `string_length`, `bit_offset`, `unit`, `scale_factor`, `offset`, `monitor_item`, `enabled`, `collect_interval`, `min_value`, `max_value`, `create_by`, `create_time`) VALUES 
-- 偏移量0-1: 设备1温度监控点 (占用2字节)
('point_001', 'plc_001', 'device_001', '设备1温度', '设备1实时温度监控', 'DB', 1, 0, 'INT', NULL, NULL, '°C', 0.1, 0, '温度', 1, 30, -50, 150, 'admin', NOW()),

-- 偏移量2-3: 设备1压力监控点 (占用2字节)
('point_002', 'plc_001', 'device_001', '设备1压力', '设备1实时压力监控', 'DB', 1, 2, 'INT', NULL, NULL, 'MPa', 0.01, 0, '压力', 1, 30, 0, 10, 'admin', NOW()),

-- 偏移量4-7: 设备1流量监控点 (占用4字节)
('point_003', 'plc_001', 'device_001', '设备1流量', '设备1实时流量监控', 'DB', 1, 4, 'REAL', NULL, NULL, 'L/min', 1.0, 0, '流量', 1, 30, 0, 1000, 'admin', NOW()),

-- 偏移量8: 设备1运行状态监控点 (占用1字节，位0)
('point_004', 'plc_001', 'device_001', '设备1运行状态', '设备1运行状态监控', 'DB', 1, 8, 'BOOL', NULL, 0, '', 1.0, 0, '运行状态', 1, 10, NULL, NULL, 'admin', NOW()),

-- 偏移量8: 设备1故障状态监控点 (占用1字节，位1)
('point_005', 'plc_001', 'device_001', '设备1故障状态', '设备1故障状态监控', 'DB', 1, 8, 'BOOL', NULL, 1, '', 1.0, 0, '故障状态', 1, 10, NULL, NULL, 'admin', NOW()),

-- 偏移量8: 设备1维护状态监控点 (占用1字节，位2)
('point_006', 'plc_001', 'device_001', '设备1维护状态', '设备1维护状态监控', 'DB', 1, 8, 'BOOL', NULL, 2, '', 1.0, 0, '维护状态', 1, 10, NULL, NULL, 'admin', NOW());

-- 示例2：设备2的监控数据点位配置（DB1数据块，偏移量10-18）
INSERT INTO `plc_data_point` (`id`, `plc_id`, `device_id`, `point_name`, `point_desc`, `data_area`, `db_number`, `start_address`, `data_type`, `string_length`, `bit_offset`, `unit`, `scale_factor`, `offset`, `monitor_item`, `enabled`, `collect_interval`, `min_value`, `max_value`, `create_by`, `create_time`) VALUES 
-- 偏移量10-11: 设备2温度监控点 (占用2字节)
('point_007', 'plc_001', 'device_002', '设备2温度', '设备2实时温度监控', 'DB', 1, 10, 'INT', NULL, NULL, '°C', 0.1, 0, '温度', 1, 30, -50, 150, 'admin', NOW()),

-- 偏移量12-13: 设备2压力监控点 (占用2字节)
('point_008', 'plc_001', 'device_002', '设备2压力', '设备2实时压力监控', 'DB', 1, 12, 'INT', NULL, NULL, 'MPa', 0.01, 0, '压力', 1, 30, 0, 10, 'admin', NOW()),

-- 偏移量14-17: 设备2流量监控点 (占用4字节)
('point_009', 'plc_001', 'device_002', '设备2流量', '设备2实时流量监控', 'DB', 1, 14, 'REAL', NULL, NULL, 'L/min', 1.0, 0, '流量', 1, 30, 0, 1000, 'admin', NOW()),

-- 偏移量18: 设备2运行状态监控点 (占用1字节，位0)
('point_010', 'plc_001', 'device_002', '设备2运行状态', '设备2运行状态监控', 'DB', 1, 18, 'BOOL', NULL, 0, '', 1.0, 0, '运行状态', 1, 10, NULL, NULL, 'admin', NOW()),

-- 偏移量18: 设备2故障状态监控点 (占用1字节，位1)
('point_011', 'plc_001', 'device_002', '设备2故障状态', '设备2故障状态监控', 'DB', 1, 18, 'BOOL', NULL, 1, '', 1.0, 0, '故障状态', 1, 10, NULL, NULL, 'admin', NOW()),

-- 偏移量18: 设备2维护状态监控点 (占用1字节，位2)
('point_012', 'plc_001', 'device_002', '设备2维护状态', '设备2维护状态监控', 'DB', 1, 18, 'BOOL', NULL, 2, '', 1.0, 0, '维护状态', 1, 10, NULL, NULL, 'admin', NOW());

-- 示例3：设备3的监控数据点位配置（DB2数据块）
INSERT INTO `plc_data_point` (`id`, `plc_id`, `device_id`, `point_name`, `point_desc`, `data_area`, `db_number`, `start_address`, `data_type`, `string_length`, `bit_offset`, `unit`, `scale_factor`, `offset`, `monitor_item`, `enabled`, `collect_interval`, `min_value`, `max_value`, `create_by`, `create_time`) VALUES 
-- 设备3电压监控点
('point_013', 'plc_001', 'device_003', '设备3电压', '设备3实时电压监控', 'DB', 2, 0, 'REAL', NULL, NULL, 'V', 1.0, 0, '电压', 1, 30, 380, 420, 'admin', NOW()),

-- 设备3电流监控点
('point_014', 'plc_001', 'device_003', '设备3电流', '设备3实时电流监控', 'DB', 2, 4, 'REAL', NULL, NULL, 'A', 1.0, 0, '电流', 1, 30, 0, 1000, 'admin', NOW()),

-- 设备3功率监控点
('point_015', 'plc_001', 'device_003', '设备3功率', '设备3实时功率监控', 'DB', 2, 8, 'REAL', NULL, NULL, 'kW', 1.0, 0, '功率', 1, 30, 0, 500, 'admin', NOW()),

-- 设备3功率因数监控点
('point_016', 'plc_001', 'device_003', '设备3功率因数', '设备3功率因数监控', 'DB', 2, 12, 'REAL', NULL, NULL, '', 1.0, 0, '功率因数', 1, 30, 0.8, 1.0, 'admin', NOW()),

-- 设备3运行状态监控点
('point_017', 'plc_001', 'device_003', '设备3运行状态', '设备3运行状态监控', 'DB', 2, 16, 'BOOL', NULL, 0, '', 1.0, 0, '运行状态', 1, 10, NULL, NULL, 'admin', NOW()),

-- 设备3故障状态监控点
('point_018', 'plc_001', 'device_003', '设备3故障状态', '设备3故障状态监控', 'DB', 2, 16, 'BOOL', NULL, 1, '', 1.0, 0, '故障状态', 1, 10, NULL, NULL, 'admin', NOW());

-- 示例4：字符串类型数据点位配置（DB3数据块）
INSERT INTO `plc_data_point` (`id`, `plc_id`, `device_id`, `point_name`, `point_desc`, `data_area`, `db_number`, `start_address`, `data_type`, `string_length`, `bit_offset`, `unit`, `scale_factor`, `offset`, `monitor_item`, `enabled`, `collect_interval`, `min_value`, `max_value`, `create_by`, `create_time`) VALUES 
-- 设备名称字符串
('point_019', 'plc_001', 'device_001', '设备名称', '设备名称字符串', 'DB', 3, 0, 'STRING', 20, NULL, '', 1.0, 0, '设备名称', 1, 300, NULL, NULL, 'admin', NOW()),

-- 设备型号字符串
('point_020', 'plc_001', 'device_001', '设备型号', '设备型号字符串', 'DB', 3, 22, 'STRING', 15, NULL, '', 1.0, 0, '设备型号', 1, 300, NULL, NULL, 'admin', NOW()),

-- 设备序列号字符串
('point_021', 'plc_001', 'device_001', '设备序列号', '设备序列号字符串', 'DB', 3, 39, 'STRING', 25, NULL, '', 1.0, 0, '设备序列号', 1, 300, NULL, NULL, 'admin', NOW());

-- 示例5：数组类型数据点位配置（DB4数据块）
INSERT INTO `plc_data_point` (`id`, `plc_id`, `device_id`, `point_name`, `point_desc`, `data_area`, `db_number`, `start_address`, `data_type`, `string_length`, `bit_offset`, `unit`, `scale_factor`, `offset`, `monitor_item`, `enabled`, `collect_interval`, `min_value`, `max_value`, `create_by`, `create_time`) VALUES 
-- 温度历史数据数组（前10个值）
('point_022', 'plc_001', 'device_001', '温度历史数据', '温度历史数据数组', 'DB', 4, 0, 'REAL', NULL, NULL, '°C', 1.0, 0, '温度历史', 1, 60, NULL, NULL, 'admin', NOW()),

-- 压力历史数据数组（前10个值）
('point_023', 'plc_001', 'device_001', '压力历史数据', '压力历史数据数组', 'DB', 4, 40, 'REAL', NULL, NULL, 'MPa', 1.0, 0, '压力历史', 1, 60, NULL, NULL, 'admin', NOW()),

-- 流量历史数据数组（前10个值）
('point_024', 'plc_001', 'device_001', '流量历史数据', '流量历史数据数组', 'DB', 4, 80, 'REAL', NULL, NULL, 'L/min', 1.0, 0, '流量历史', 1, 60, NULL, NULL, 'admin', NOW());

-- 查询示例：根据设备ID查询所有数据点位配置
-- SELECT * FROM plc_data_point WHERE device_id = 'device_001' ORDER BY start_address;

-- 查询示例：根据PLC ID和数据块号查询所有数据点位配置
-- SELECT * FROM plc_data_point WHERE plc_id = 'plc_001' AND db_number = 1 ORDER BY start_address;

-- 查询示例：根据偏移量范围查询数据点位配置
-- SELECT * FROM plc_data_point WHERE plc_id = 'plc_001' AND db_number = 1 AND start_address BETWEEN 0 AND 20 ORDER BY start_address;

-- 查询示例：获取特定数据类型的点位配置
-- SELECT * FROM plc_data_point WHERE plc_id = 'plc_001' AND data_type = 'REAL' ORDER BY start_address;

-- 查询示例：获取BOOL类型的点位配置（包含位偏移信息）
-- SELECT * FROM plc_data_point WHERE plc_id = 'plc_001' AND data_type = 'BOOL' ORDER BY start_address, bit_offset; 