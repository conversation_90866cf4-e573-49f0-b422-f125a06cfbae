# PLC数据点位字段含义详解

## 容易混淆的三个"偏移量"

### 1. start_address - 字节位置偏移量（这个就是我们常说的偏移量！）
```sql
start_address = 0   -- 从第0个字节开始读取数据
start_address = 2   -- 从第2个字节开始读取数据  
start_address = 4   -- 从第4个字节开始读取数据
```

### 2. offset - 数值校正偏移量
```sql
offset = 0     -- 不需要校正
offset = -10   -- 原始值减去10
offset = 5.5   -- 原始值加上5.5
```

### 3. bit_offset - 位偏移量（仅BOOL类型）
```sql
bit_offset = 0   -- 该字节的第0位（最低位）
bit_offset = 1   -- 该字节的第1位
bit_offset = 7   -- 该字节的第7位（最高位）
```

## 实际数据对应关系

### 原始PLC数据（字节数组）
```
字节位置: 0    1    2    3    4    5    6    7    8    9   10   11   12   13   14   15   16   17   18   19
原始数据: FF   00   78   00   42   C8   80   00   01   00   2E   01   96   00   42   AA   66   66   00   00
```

### 数据点位配置示例

#### 设备1温度
```sql
start_address = 0      -- 从字节0开始读取
data_type = 'INT'      -- 读取2个字节 (字节0和字节1)
scale_factor = 0.1     -- 缩放因子
offset = 0             -- 数值校正偏移量

计算过程:
1. 读取字节0-1: FF 00 → 转换为整数: 255
2. 应用公式: 实际值 = (255 × 0.1) + 0 = 25.5°C
```

#### 设备1压力  
```sql
start_address = 2      -- 从字节2开始读取
data_type = 'INT'      -- 读取2个字节 (字节2和字节3)
scale_factor = 0.01    -- 缩放因子
offset = 0             -- 数值校正偏移量

计算过程:
1. 读取字节2-3: 78 00 → 转换为整数: 120
2. 应用公式: 实际值 = (120 × 0.01) + 0 = 1.2MPa
```

#### 设备1流量
```sql
start_address = 4      -- 从字节4开始读取
data_type = 'REAL'     -- 读取4个字节 (字节4-7)
scale_factor = 1.0     -- 缩放因子
offset = 0             -- 数值校正偏移量

计算过程:
1. 读取字节4-7: 42 C8 80 00 → 转换为浮点数: 100.5
2. 应用公式: 实际值 = (100.5 × 1.0) + 0 = 100.5L/min
```

#### 设备1运行状态
```sql
start_address = 8      -- 从字节8开始读取
data_type = 'BOOL'     -- 读取1个字节 (字节8)
bit_offset = 0         -- 读取该字节的第0位
scale_factor = 1.0     -- 缩放因子（BOOL类型通常为1）
offset = 0             -- 数值校正偏移量

计算过程:
1. 读取字节8: 01 → 二进制: 00000001
2. 读取第0位: 1 → true (运行中)
```

#### 设备1故障状态
```sql
start_address = 8      -- 从字节8开始读取（和运行状态同一个字节！）
data_type = 'BOOL'     -- 读取1个字节 (字节8)
bit_offset = 1         -- 读取该字节的第1位
scale_factor = 1.0     -- 缩放因子
offset = 0             -- 数值校正偏移量

计算过程:
1. 读取字节8: 01 → 二进制: 00000001  
2. 读取第1位: 0 → false (无故障)
```

## offset数值校正的实际应用

### 场景1：传感器零点校正
```sql
-- 温度传感器有-2°C的零点偏移
start_address = 0
scale_factor = 0.1
offset = -2.0          -- 校正偏移量

计算: 实际温度 = (原始值 × 0.1) + (-2.0)
如果原始值是270，实际温度 = (270 × 0.1) - 2.0 = 25.0°C
```

### 场景2：单位换算
```sql  
-- 压力传感器读数需要加上大气压
start_address = 2
scale_factor = 0.01
offset = 1.013         -- 加上标准大气压

计算: 绝对压力 = (原始值 × 0.01) + 1.013
如果原始值是120，绝对压力 = (120 × 0.01) + 1.013 = 2.213MPa
```

## 总结对应关系

| 字段名 | 含义 | 作用 | 示例 |
|--------|------|------|------|
| **start_address** | **字节位置偏移量** | **确定数据在字节数组中的位置** | **0表示从第0字节开始** |
| offset | 数值校正偏移量 | 校正传感器误差或单位换算 | -2.0表示减去2 |
| bit_offset | 位偏移量 | 确定BOOL数据在字节中的位位置 | 0表示最低位 |
| scale_factor | 缩放因子 | 将原始整数转换为实际数值 | 0.1表示除以10 |

**关键理解：**
- `start_address` = 我们常说的"偏移量"，决定从哪个字节开始读
- `offset` = 数值校正，用于最终计算中的加减
- 数据读取位置完全由 `start_address` 和 `data_type` 决定
- 最终数值 = (原始值 × scale_factor) + offset