-- 设备监控系统测试数据（包含S7 PLC配置）

-- 清空现有数据
DELETE FROM plc_data_point;
DELETE FROM plc_config;
DELETE FROM device_monitor_data;
DELETE FROM device_monitor;

-- 插入PLC配置数据
INSERT INTO `plc_config` (`id`, `plc_name`, `ip_address`, `port`, `rack`, `slot`, `connection_timeout`, `read_timeout`, `enabled`, `connection_status`, `last_connection_time`, `remark`, `create_by`, `create_time`) VALUES
('plc_001', '变电站A区PLC', '*************', 102, 0, 1, 5000, 3000, 1, 1, NOW(), '变电站A区主控PLC', 'admin', NOW()),
('plc_002', '变电站B区PLC', '*************', 102, 0, 1, 5000, 3000, 1, 1, NOW(), '变电站B区主控PLC', 'admin', NOW()),
('plc_003', '变电站C区PLC', '*************', 102, 0, 1, 5000, 3000, 1, 0, DATE_SUB(NOW(), INTERVAL 1 HOUR), '变电站C区主控PLC', 'admin', NOW());

-- 插入更多测试设备数据
INSERT INTO `device_monitor` (`id`, `device_code`, `device_name`, `device_type`, `status`, `run_status`, `location`, `load_capacity`, `oil_analysis`, `cooling_life`, `intelligent_evaluation`, `voltage`, `current`, `temperature`, `last_update_time`, `create_by`, `create_time`) VALUES 
('device_001', 'DEV001', '主变压器A', 'transformer', 1, 1, '变电站A区1号位', '正常', '正常', '5年', '正常', 500.00, 120.50, 35.2, NOW(), 'admin', NOW()),
('device_002', 'DEV002', '主变压器B', 'transformer', 1, 1, '变电站A区2号位', '正常', '正常', '3年', '正常', 500.00, 115.30, 36.1, NOW(), 'admin', NOW()),
('device_003', 'DEV003', '开关柜1号', 'switchgear', 1, 1, '变电站B区1号位', '正常', '正常', '8年', '正常', 500.00, 0.00, 32.5, NOW(), 'admin', NOW()),
('device_004', 'DEV004', '开关柜2号', 'switchgear', 2, 2, '变电站B区2号位', '异常', '正常', '2年', '异常', 480.00, 0.00, 45.8, NOW(), 'admin', NOW()),
('device_005', 'DEV005', '电容器组A', 'capacitor', 1, 1, '变电站C区1号位', '正常', '正常', '6年', '正常', 500.00, 85.20, 28.9, NOW(), 'admin', NOW()),
('device_006', 'DEV006', '电容器组B', 'capacitor', 1, 1, '变电站C区2号位', '正常', '正常', '4年', '正常', 500.00, 88.70, 30.1, NOW(), 'admin', NOW()),
('device_007', 'DEV007', '电抗器A', 'reactor', 1, 1, '变电站D区1号位', '正常', '正常', '7年', '正常', 500.00, 95.40, 33.8, NOW(), 'admin', NOW()),
('device_008', 'DEV008', '电抗器B', 'reactor', 3, 0, '变电站D区2号位', '正常', '正常', '9年', '正常', 0.00, 0.00, 25.0, NOW(), 'admin', NOW()),
('device_009', 'DEV009', '主变压器C', 'transformer', 1, 1, '变电站E区1号位', '正常', '良好', '2年', '正常', 500.00, 110.20, 34.5, NOW(), 'admin', NOW()),
('device_010', 'DEV010', '开关柜3号', 'switchgear', 0, 0, '变电站E区2号位', '未知', '未知', '未知', '未知', 0.00, 0.00, 0.0, DATE_SUB(NOW(), INTERVAL 2 HOUR), 'admin', NOW());

-- 插入监控数据（最近24小时的数据）
INSERT INTO `device_monitor_data` (`id`, `device_id`, `device_code`, `monitor_item`, `monitor_value`, `unit`, `min_value`, `max_value`, `status`, `monitor_time`, `create_time`) VALUES 
-- 设备001的监控数据
('data_001_001', 'device_001', 'DEV001', '负载能力', 85.5, '%', 0, 100, 0, NOW(), NOW()),
('data_001_002', 'device_001', 'DEV001', '油位清洁', 95.2, '%', 80, 100, 0, NOW(), NOW()),
('data_001_003', 'device_001', 'DEV001', '制冷效率', 92.8, '%', 85, 100, 0, NOW(), NOW()),
('data_001_004', 'device_001', 'DEV001', '绝缘电阻', 1500.0, 'MΩ', 1000, 2000, 0, NOW(), NOW()),
('data_001_005', 'device_001', 'DEV001', '负载能力', 83.2, '%', 0, 100, 0, DATE_SUB(NOW(), INTERVAL 1 HOUR), NOW()),
('data_001_006', 'device_001', 'DEV001', '油位清洁', 94.8, '%', 80, 100, 0, DATE_SUB(NOW(), INTERVAL 1 HOUR), NOW()),

-- 设备002的监控数据
('data_002_001', 'device_002', 'DEV002', '负载能力', 78.3, '%', 0, 100, 0, NOW(), NOW()),
('data_002_002', 'device_002', 'DEV002', '油位清洁', 88.7, '%', 80, 100, 0, NOW(), NOW()),
('data_002_003', 'device_002', 'DEV002', '制冷效率', 89.5, '%', 85, 100, 0, NOW(), NOW()),
('data_002_004', 'device_002', 'DEV002', '绝缘电阻', 1350.0, 'MΩ', 1000, 2000, 0, NOW(), NOW()),

-- 设备003的监控数据
('data_003_001', 'device_003', 'DEV003', '负载能力', 65.4, '%', 0, 100, 0, NOW(), NOW()),
('data_003_002', 'device_003', 'DEV003', '开关次数', 1250, '次', 0, 10000, 0, NOW(), NOW()),
('data_003_003', 'device_003', 'DEV003', '绝缘电阻', 1800.0, 'MΩ', 1000, 2000, 0, NOW(), NOW()),
('data_003_004', 'device_003', 'DEV003', '接触电阻', 45.2, 'μΩ', 0, 100, 0, NOW(), NOW()),

-- 设备004的监控数据（异常设备）
('data_004_001', 'device_004', 'DEV004', '负载能力', 45.2, '%', 0, 100, 1, NOW(), NOW()),
('data_004_002', 'device_004', 'DEV004', '开关次数', 8500, '次', 0, 10000, 1, NOW(), NOW()),
('data_004_003', 'device_004', 'DEV004', '绝缘电阻', 800.0, 'MΩ', 1000, 2000, 2, NOW(), NOW()),
('data_004_004', 'device_004', 'DEV004', '接触电阻', 120.5, 'μΩ', 0, 100, 2, NOW(), NOW()),

-- 设备005的监控数据
('data_005_001', 'device_005', 'DEV005', '负载能力', 92.1, '%', 0, 100, 0, NOW(), NOW()),
('data_005_002', 'device_005', 'DEV005', '电容值', 98.5, '%', 95, 105, 0, NOW(), NOW()),
('data_005_003', 'device_005', 'DEV005', '绝缘电阻', 1650.0, 'MΩ', 1000, 2000, 0, NOW(), NOW()),
('data_005_004', 'device_005', 'DEV005', '介质损耗', 0.15, '%', 0, 0.5, 0, NOW(), NOW()),

-- 设备006的监控数据
('data_006_001', 'device_006', 'DEV006', '负载能力', 87.6, '%', 0, 100, 0, NOW(), NOW()),
('data_006_002', 'device_006', 'DEV006', '电容值', 102.3, '%', 95, 105, 0, NOW(), NOW()),
('data_006_003', 'device_006', 'DEV006', '绝缘电阻', 1420.0, 'MΩ', 1000, 2000, 0, NOW(), NOW()),
('data_006_004', 'device_006', 'DEV006', '介质损耗', 0.22, '%', 0, 0.5, 0, NOW(), NOW()),

-- 设备007的监控数据
('data_007_001', 'device_007', 'DEV007', '负载能力', 76.8, '%', 0, 100, 0, NOW(), NOW()),
('data_007_002', 'device_007', 'DEV007', '电感值', 99.2, '%', 95, 105, 0, NOW(), NOW()),
('data_007_003', 'device_007', 'DEV007', '绝缘电阻', 1580.0, 'MΩ', 1000, 2000, 0, NOW(), NOW()),
('data_007_004', 'device_007', 'DEV007', '损耗因数', 0.18, '%', 0, 0.3, 0, NOW(), NOW()),

-- 设备009的监控数据
('data_009_001', 'device_009', 'DEV009', '负载能力', 91.3, '%', 0, 100, 0, NOW(), NOW()),
('data_009_002', 'device_009', 'DEV009', '油位清洁', 97.8, '%', 80, 100, 0, NOW(), NOW()),
('data_009_003', 'device_009', 'DEV009', '制冷效率', 94.2, '%', 85, 100, 0, NOW(), NOW()),
('data_009_004', 'device_009', 'DEV009', '绝缘电阻', 1720.0, 'MΩ', 1000, 2000, 0, NOW(), NOW());

-- 插入历史数据（用于趋势分析）
INSERT INTO `device_monitor_data` (`id`, `device_id`, `device_code`, `monitor_item`, `monitor_value`, `unit`, `min_value`, `max_value`, `status`, `monitor_time`, `create_time`) VALUES 
-- 过去2小时的数据
('hist_001_001', 'device_001', 'DEV001', '负载能力', 82.1, '%', 0, 100, 0, DATE_SUB(NOW(), INTERVAL 2 HOUR), NOW()),
('hist_001_002', 'device_001', 'DEV001', '负载能力', 84.5, '%', 0, 100, 0, DATE_SUB(NOW(), INTERVAL 3 HOUR), NOW()),
('hist_001_003', 'device_001', 'DEV001', '负载能力', 86.2, '%', 0, 100, 0, DATE_SUB(NOW(), INTERVAL 4 HOUR), NOW()),
('hist_001_004', 'device_001', 'DEV001', '负载能力', 83.8, '%', 0, 100, 0, DATE_SUB(NOW(), INTERVAL 5 HOUR), NOW()),
('hist_001_005', 'device_001', 'DEV001', '负载能力', 85.9, '%', 0, 100, 0, DATE_SUB(NOW(), INTERVAL 6 HOUR), NOW()),

-- 温度历史数据
('hist_temp_001', 'device_001', 'DEV001', '温度', 34.8, '℃', 0, 80, 0, DATE_SUB(NOW(), INTERVAL 1 HOUR), NOW()),
('hist_temp_002', 'device_001', 'DEV001', '温度', 35.5, '℃', 0, 80, 0, DATE_SUB(NOW(), INTERVAL 2 HOUR), NOW()),
('hist_temp_003', 'device_001', 'DEV001', '温度', 36.1, '℃', 0, 80, 0, DATE_SUB(NOW(), INTERVAL 3 HOUR), NOW()),
('hist_temp_004', 'device_001', 'DEV001', '温度', 35.8, '℃', 0, 80, 0, DATE_SUB(NOW(), INTERVAL 4 HOUR), NOW()),

-- 电压历史数据
('hist_volt_001', 'device_001', 'DEV001', '电压', 498.5, 'V', 450, 550, 0, DATE_SUB(NOW(), INTERVAL 1 HOUR), NOW()),
('hist_volt_002', 'device_001', 'DEV001', '电压', 501.2, 'V', 450, 550, 0, DATE_SUB(NOW(), INTERVAL 2 HOUR), NOW()),
('hist_volt_003', 'device_001', 'DEV001', '电压', 499.8, 'V', 450, 550, 0, DATE_SUB(NOW(), INTERVAL 3 HOUR), NOW()),
('hist_volt_004', 'device_001', 'DEV001', '电压', 502.1, 'V', 450, 550, 0, DATE_SUB(NOW(), INTERVAL 4 HOUR), NOW());

-- 更新设备最后更新时间
UPDATE device_monitor SET last_update_time = NOW() WHERE status = 1;
UPDATE device_monitor SET last_update_time = DATE_SUB(NOW(), INTERVAL 2 HOUR) WHERE status = 0;
UPDATE device_monitor SET last_update_time = DATE_SUB(NOW(), INTERVAL 30 MINUTE) WHERE status = 2;
UPDATE device_monitor SET last_update_time = DATE_SUB(NOW(), INTERVAL 1 HOUR) WHERE status = 3;

-- 插入PLC数据点位配置
INSERT INTO `plc_data_point` (`id`, `plc_id`, `device_id`, `point_name`, `point_desc`, `data_area`, `db_number`, `start_address`, `data_type`, `string_length`, `bit_offset`, `unit`, `scale_factor`, `offset`, `monitor_item`, `enabled`, `collect_interval`, `min_value`, `max_value`, `create_by`, `create_time`) VALUES
-- 设备001的数据点位
('point_001_001', 'plc_001', 'device_001', 'voltage', '电压', 'DB', 1, 0, 'REAL', NULL, NULL, 'V', 1, 0, '电压', 1, 30, 450, 550, 'admin', NOW()),
('point_001_002', 'plc_001', 'device_001', 'current', '电流', 'DB', 1, 4, 'REAL', NULL, NULL, 'A', 1, 0, '电流', 1, 30, 0, 200, 'admin', NOW()),
('point_001_003', 'plc_001', 'device_001', 'temperature', '温度', 'DB', 1, 8, 'REAL', NULL, NULL, '℃', 1, 0, '温度', 1, 30, 0, 80, 'admin', NOW()),
('point_001_004', 'plc_001', 'device_001', 'load_capacity', '负载能力', 'DB', 1, 12, 'REAL', NULL, NULL, '%', 1, 0, '负载能力', 1, 30, 0, 100, 'admin', NOW()),
('point_001_005', 'plc_001', 'device_001', 'oil_level', '油位', 'DB', 1, 16, 'REAL', NULL, NULL, '%', 1, 0, '油位清洁', 1, 30, 80, 100, 'admin', NOW()),
('point_001_006', 'plc_001', 'device_001', 'cooling_efficiency', '制冷效率', 'DB', 1, 20, 'REAL', NULL, NULL, '%', 1, 0, '制冷效率', 1, 30, 85, 100, 'admin', NOW()),
('point_001_007', 'plc_001', 'device_001', 'insulation_resistance', '绝缘电阻', 'DB', 1, 24, 'REAL', NULL, NULL, 'MΩ', 1, 0, '绝缘电阻', 1, 30, 1000, 2000, 'admin', NOW()),
('point_001_008', 'plc_001', 'device_001', 'status', '设备状态', 'DB', 1, 28, 'INT', NULL, NULL, NULL, 1, 0, '状态', 1, 30, 0, 3, 'admin', NOW()),
('point_001_009', 'plc_001', 'device_001', 'run_status', '运行状态', 'DB', 1, 30, 'INT', NULL, NULL, NULL, 1, 0, '运行状态', 1, 30, 0, 2, 'admin', NOW()),
('point_001_010', 'plc_001', 'device_001', 'alarm', '报警状态', 'DB', 1, 32, 'BOOL', NULL, 0, NULL, 1, 0, '报警状态', 1, 30, 0, 1, 'admin', NOW()),

-- 设备002的数据点位
('point_002_001', 'plc_001', 'device_002', 'voltage', '电压', 'DB', 2, 0, 'REAL', NULL, NULL, 'V', 1, 0, '电压', 1, 30, 450, 550, 'admin', NOW()),
('point_002_002', 'plc_001', 'device_002', 'current', '电流', 'DB', 2, 4, 'REAL', NULL, NULL, 'A', 1, 0, '电流', 1, 30, 0, 200, 'admin', NOW()),
('point_002_003', 'plc_001', 'device_002', 'temperature', '温度', 'DB', 2, 8, 'REAL', NULL, NULL, '℃', 1, 0, '温度', 1, 30, 0, 80, 'admin', NOW()),
('point_002_004', 'plc_001', 'device_002', 'load_capacity', '负载能力', 'DB', 2, 12, 'REAL', NULL, NULL, '%', 1, 0, '负载能力', 1, 30, 0, 100, 'admin', NOW()),
('point_002_005', 'plc_001', 'device_002', 'oil_level', '油位', 'DB', 2, 16, 'REAL', NULL, NULL, '%', 1, 0, '油位清洁', 1, 30, 80, 100, 'admin', NOW()),

-- 设备003的数据点位
('point_003_001', 'plc_002', 'device_003', 'voltage', '电压', 'DB', 3, 0, 'REAL', NULL, NULL, 'V', 1, 0, '电压', 1, 30, 450, 550, 'admin', NOW()),
('point_003_002', 'plc_002', 'device_003', 'current', '电流', 'DB', 3, 4, 'REAL', NULL, NULL, 'A', 1, 0, '电流', 1, 30, 0, 200, 'admin', NOW()),
('point_003_003', 'plc_002', 'device_003', 'temperature', '温度', 'DB', 3, 8, 'REAL', NULL, NULL, '℃', 1, 0, '温度', 1, 30, 0, 80, 'admin', NOW()),
('point_003_004', 'plc_002', 'device_003', 'switch_count', '开关次数', 'DB', 3, 12, 'DINT', NULL, NULL, '次', 1, 0, '开关次数', 1, 30, 0, 10000, 'admin', NOW()),
('point_003_005', 'plc_002', 'device_003', 'contact_resistance', '接触电阻', 'DB', 3, 16, 'REAL', NULL, NULL, 'μΩ', 1, 0, '接触电阻', 1, 30, 0, 100, 'admin', NOW()),

-- 设备004的数据点位
('point_004_001', 'plc_002', 'device_004', 'voltage', '电压', 'DB', 4, 0, 'REAL', NULL, NULL, 'V', 1, 0, '电压', 1, 30, 450, 550, 'admin', NOW()),
('point_004_002', 'plc_002', 'device_004', 'current', '电流', 'DB', 4, 4, 'REAL', NULL, NULL, 'A', 1, 0, '电流', 1, 30, 0, 200, 'admin', NOW()),
('point_004_003', 'plc_002', 'device_004', 'temperature', '温度', 'DB', 4, 8, 'REAL', NULL, NULL, '℃', 1, 0, '温度', 1, 30, 0, 80, 'admin', NOW()),
('point_004_004', 'plc_002', 'device_004', 'switch_count', '开关次数', 'DB', 4, 12, 'DINT', NULL, NULL, '次', 1, 0, '开关次数', 1, 30, 0, 10000, 'admin', NOW()),
('point_004_005', 'plc_002', 'device_004', 'contact_resistance', '接触电阻', 'DB', 4, 16, 'REAL', NULL, NULL, 'μΩ', 1, 0, '接触电阻', 1, 30, 0, 100, 'admin', NOW()),

-- 设备005的数据点位
('point_005_001', 'plc_003', 'device_005', 'voltage', '电压', 'DB', 5, 0, 'REAL', NULL, NULL, 'V', 1, 0, '电压', 1, 30, 450, 550, 'admin', NOW()),
('point_005_002', 'plc_003', 'device_005', 'current', '电流', 'DB', 5, 4, 'REAL', NULL, NULL, 'A', 1, 0, '电流', 1, 30, 0, 200, 'admin', NOW()),
('point_005_003', 'plc_003', 'device_005', 'temperature', '温度', 'DB', 5, 8, 'REAL', NULL, NULL, '℃', 1, 0, '温度', 1, 30, 0, 80, 'admin', NOW()),
('point_005_004', 'plc_003', 'device_005', 'capacitance', '电容值', 'DB', 5, 12, 'REAL', NULL, NULL, '%', 1, 0, '电容值', 1, 30, 95, 105, 'admin', NOW()),
('point_005_005', 'plc_003', 'device_005', 'dielectric_loss', '介质损耗', 'DB', 5, 16, 'REAL', NULL, NULL, '%', 1, 0, '介质损耗', 1, 30, 0, 0.5, 'admin', NOW());
