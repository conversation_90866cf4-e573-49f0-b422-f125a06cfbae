# PLC数据偏移量读取说明

## 概述

在PLC（可编程逻辑控制器）系统中，一个数据块（DB）通常包含多个设备或变量的数据。通过偏移量（Offset）可以精确定位和读取数据块中的特定数据。

## 数据结构示例

### DB1数据块结构（示例）
```
偏移量    数据类型    数据名称        说明
0-1      INT        设备1温度      温度值（单位：0.1°C）
2-3      INT        设备1压力      压力值（单位：0.01MPa）
4-7      REAL       设备1流量      流量值（单位：L/min）
8-9      BOOL       设备1状态      位0：运行状态，位1：故障状态，位2：维护状态
10-11    INT        设备2温度      温度值（单位：0.1°C）
12-13    INT        设备2压力      压力值（单位：0.01MPa）
14-17    REAL       设备2流量      流量值（单位：L/min）
18-19    BOOL       设备2状态      位0：运行状态，位1：故障状态，位2：维护状态
```

## 数据库配置

### 1. PLC配置表（plc_config）
```sql
-- 配置PLC连接信息
INSERT INTO plc_config (id, plc_name, ip_address, port, rack, slot) VALUES 
('plc_001', '主控PLC', '*************', 102, 0, 1);
```

### 2. 数据点位配置表（plc_data_point）
```sql
-- 配置数据点位，指定偏移量
INSERT INTO plc_data_point (
    id, plc_id, device_id, point_name, 
    data_area, db_number, start_address, data_type, 
    bit_offset, unit, scale_factor, monitor_item
) VALUES 
-- 设备1温度（偏移量0）
('point_001', 'plc_001', 'device_001', '设备1温度', 
 'DB', 1, 0, 'INT', NULL, '°C', 0.1, '温度'),

-- 设备1压力（偏移量2）
('point_002', 'plc_001', 'device_001', '设备1压力', 
 'DB', 1, 2, 'INT', NULL, 'MPa', 0.01, '压力'),

-- 设备1流量（偏移量4）
('point_003', 'plc_001', 'device_001', '设备1流量', 
 'DB', 1, 4, 'REAL', NULL, 'L/min', 1.0, '流量'),

-- 设备1运行状态（偏移量8，位0）
('point_004', 'plc_001', 'device_001', '设备1运行状态', 
 'DB', 1, 8, 'BOOL', 0, '', 1.0, '运行状态'),

-- 设备1故障状态（偏移量8，位1）
('point_005', 'plc_001', 'device_001', '设备1故障状态', 
 'DB', 1, 8, 'BOOL', 1, '', 1.0, '故障状态');
```

## API使用示例

### 1. 根据偏移量读取PLC数据
```http
GET /shebei/deviceMonitor/readPlcDataByOffset?plcId=plc_001&dbNumber=1&startAddress=0&dataLength=20
```

**响应示例：**
```json
{
  "success": true,
  "result": {
    "plcId": "plc_001",
    "dbNumber": 1,
    "startAddress": 0,
    "dataLength": 20,
    "rawData": "FF 00 78 00 42 C8 80 00 01 00 2E 01 96 00 42 AA 66 66 00 00",
    "parsedData": {
      "设备1": {
        "temperature": 25.5,
        "pressure": 1.2,
        "flow": 100.5,
        "status": "运行"
      },
      "设备2": {
        "temperature": 30.2,
        "pressure": 1.5,
        "flow": 85.3,
        "status": "停止"
      }
    },
    "readTime": "2025-01-01T10:30:00"
  }
}
```

### 2. 读取特定设备的数据
```http
GET /shebei/deviceMonitor/readDeviceData?deviceId=device_001
```

**响应示例：**
```json
{
  "success": true,
  "result": {
    "deviceId": "device_001",
    "dataPoints": [
      {
        "pointName": "温度",
        "dataArea": "DB",
        "dbNumber": 1,
        "startAddress": 0,
        "dataType": "INT",
        "unit": "°C",
        "scaleFactor": 0.1
      }
    ],
    "deviceData": [
      {
        "pointName": "温度",
        "value": 25.5,
        "unit": "°C",
        "dataType": "INT",
        "readTime": "2025-01-01T10:30:00"
      }
    ],
    "readTime": "2025-01-01T10:30:00"
  }
}
```

## 数据类型说明

### 1. 基本数据类型
- **BOOL**: 布尔型，1字节，支持位偏移
- **BYTE**: 字节型，1字节
- **WORD**: 字型，2字节
- **INT**: 整型，2字节
- **DWORD**: 双字型，4字节
- **DINT**: 双整型，4字节
- **REAL**: 实数型，4字节
- **STRING**: 字符串，可变长度

### 2. 位偏移（BOOL类型）
对于BOOL类型数据，可以在同一个字节中存储多个布尔值：
```sql
-- 偏移量8的字节包含多个状态位
-- 位0：运行状态
-- 位1：故障状态
-- 位2：维护状态
-- 位3：报警状态
```

## 实际应用场景

### 1. 批量读取优化
```java
// 一次性读取整个数据块，然后根据偏移量解析
byte[] rawData = plcClient.readDB(1, 0, 100); // 读取DB1的前100字节

// 根据配置的偏移量解析各个数据点
for (PlcDataPoint point : dataPoints) {
    Object value = parseDataByOffset(rawData, point);
    // 处理数据...
}
```

### 2. 数据块结构设计
```sql
-- 建议的数据块结构设计
-- DB1: 设备实时数据（偏移量0-99）
-- DB2: 设备历史数据（偏移量0-199）
-- DB3: 设备配置信息（偏移量0-49）
-- DB4: 报警信息（偏移量0-99）
```

### 3. 错误处理
```java
try {
    // 读取PLC数据
    byte[] data = plcClient.readDB(dbNumber, startAddress, dataLength);
    
    // 验证数据长度
    if (data.length < expectedLength) {
        throw new PlcDataException("数据长度不足");
    }
    
    // 解析数据
    Object value = parseData(data, dataType, scaleFactor);
    
} catch (PlcConnectionException e) {
    // 处理连接异常
    log.error("PLC连接失败", e);
} catch (PlcDataException e) {
    // 处理数据异常
    log.error("PLC数据读取失败", e);
}
```

## 注意事项

1. **偏移量计算**：确保偏移量计算正确，避免数据重叠
2. **数据类型匹配**：确保配置的数据类型与实际PLC数据类型一致
3. **字节序**：注意PLC的字节序（大端序/小端序）
4. **数据有效性**：添加数据范围检查和异常值处理
5. **性能优化**：批量读取数据，减少网络通信次数

## 常见问题

### Q1: 如何确定正确的偏移量？
A1: 需要查看PLC程序的数据块定义，或者使用PLC编程软件查看数据块结构。

### Q2: 如何处理不同PLC厂商的数据格式？
A2: 不同厂商的PLC可能有不同的数据格式，需要根据具体厂商的协议进行适配。

### Q3: 如何优化数据读取性能？
A3: 可以批量读取连续的数据块，减少网络通信次数，提高读取效率。

### Q4: 如何处理数据读取失败？
A4: 实现重试机制，记录错误日志，设置数据有效性检查。 